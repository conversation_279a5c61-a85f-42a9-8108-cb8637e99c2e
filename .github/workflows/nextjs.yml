name: Frontend CI/CD

on:
  push:
    tags:
      - "v*"

jobs:
  lint:
    name: <PERSON><PERSON>
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: ./app
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: "20"
          cache: "npm"
          cache-dependency-path: ./app/package-lock.json
      - name: Install dependencies
        run: npm ci
      - name: Run linter
        run: npm run lint
  build:
    name: Build
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: ./app
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: "20"
          cache: "npm"
          cache-dependency-path: ./app/package-lock.json
      - name: Install dependencies
        run: npm ci
      - name: Cache Next.js build
        uses: actions/cache@v3
        with:
          path: |
            ./app/.next/cache
          key: ${{ runner.os }}-nextjs-${{ hashFiles('./app/**/*') }}
      - name: Build project
        run: npm run build

  deploy:
    name: Deploy to Vercel
    needs: [build]
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: ./app
    steps:
      - uses: actions/checkout@v4

      - uses: actions/setup-node@v4
        with:
          node-version: "20"
          cache: "npm"
          cache-dependency-path: ./app/package-lock.json

      - name: Install dependencies
        run: npm ci

      - name: Install Vercel CLI
        run: npm install --global vercel@latest

      - name: Pull Vercel Environment Information
        run: vercel pull --yes --environment=production --token=${{ secrets.VERCEL_TOKEN }}

      - name: Build Project Artifacts
        run: vercel build --prod --token=${{ secrets.VERCEL_TOKEN }}

      - name: Deploy Project Artifacts to Vercel
        run: vercel deploy --prebuilt --prod --token=${{ secrets.VERCEL_TOKEN }} --scope=${{ secrets.VERCEL_SCOPE }}
