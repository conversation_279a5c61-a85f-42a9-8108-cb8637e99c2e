datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

generator client {
  provider = "prisma-client-js"
  output   = "../node_modules/.prisma/client"
}

enum Language {
  EN
  VI
}

enum Provider {
  TELEGRAM
  USERNAME_PASSWORD
}

enum PartsOfSpeech {
  NOUN
  VERB
  ADJECTIVE
  ADVERB
  PRONOUN
  PREPOSITION
  CONJUNCTION
  INTERJECTION
}

enum Difficulty {
  BEGINNER
  INTERMEDIATE
  ADVANCED
}

enum Length {
  SHORT
  MEDIUM
  LONG
}

model User {
  id              String            @id() @default(uuid())
  provider        Provider
  provider_id     String
  username        String?
  password_hash   String?
  disabled        Boolean           @default(false)
  collections     Collection[]
  last_seen_words LastSeenWord[]
  keywords        Keyword[]
  feedbacks       Feedback[]
  collection_stats CollectionStats[]
  created_at      DateTime          @default(now())
  updated_at      DateTime          @updatedAt()

  @@unique([provider, provider_id])
  @@unique([username])
}

model Word {
  id          String       @id() @default(uuid())
  term        String
  language    Language
  audio_url   String?
  definitions Definition[]
  created_at  DateTime     @default(now())
  updated_at  DateTime     @updatedAt()

  @@unique([term, language])
}

model Definition {
  id       String          @id() @default(uuid())
  word     Word            @relation(fields: [word_id], references: [id])
  word_id  String
  pos      PartsOfSpeech[]
  ipa      String
  images   String[]
  explains Explain[]
  examples Example[]
}

model Explain {
  id            String     @id() @default(uuid())
  EN            String
  VI            String
  definition    Definition @relation(fields: [definition_id], references: [id])
  definition_id String
}

model Example {
  id            String     @id() @default(uuid())
  EN            String
  VI            String
  definition    Definition @relation(fields: [definition_id], references: [id])
  definition_id String
}

model Keyword {
  id      String @id() @default(uuid())
  content String
  user    User   @relation(fields: [user_id], references: [id])
  user_id String

  @@index([user_id])
}

model Collection {
  id                              String            @id() @default(uuid())
  name                            String
  target_language                 Language          @default(EN)
  source_language                 Language          @default(VI)
  user                            User              @relation(fields: [user_id], references: [id])
  user_id                         String
  word_ids                        String[]
  paragraph_ids                   String[]
  keyword_ids                     String[]
  enable_learn_word_notification  Boolean           @default(false)
  collection_stats                CollectionStats[]
  created_at                      DateTime          @default(now())
  updated_at                      DateTime          @updatedAt()

  @@index([user_id])
}

model LastSeenWord {
  id           String   @id() @default(uuid())
  last_seen_at DateTime @default(now())
  review_count Int      @default(0)
  user         User     @relation(fields: [user_id], references: [id])
  user_id      String
  word_id      String

  @@unique([user_id, word_id])
  @@index([user_id])
}

model Paragraph {
  id         String     @id() @default(uuid())
  content    String
  difficulty Difficulty
  language   Language
  length     Length
  multiple_choice_exercises  MultipleChoiceExercise[]

  @@index([language])
  @@index([difficulty])
}

model MultipleChoiceExercise {
  id           String       @id() @default(uuid())
  question     String
  options      String[]
  answer       Int // index of the correct answer in the options array
  explanation  String?
  paragraph_id String
  paragraph    Paragraph    @relation(fields: [paragraph_id], references: [id])

  @@index([paragraph_id])
}

model Feedback {
  id         String   @id() @default(uuid())
  message    String
  user_id    String
  user       User     @relation(fields: [user_id], references: [id])
  created_at DateTime @default(now())

  @@index([user_id])
}

model CollectionStats {
  id                        String     @id() @default(uuid())
  collection_id             String
  collection                Collection @relation(fields: [collection_id], references: [id], onDelete: Cascade)
  user_id                   String
  user                      User       @relation(fields: [user_id], references: [id], onDelete: Cascade)
  date                      DateTime   @db.Date
  words_reviewed_count      Int        @default(0)
  qa_practice_submissions   Int        @default(0)
  paragraph_practice_submissions Int   @default(0)
  created_at                DateTime   @default(now())
  updated_at                DateTime   @updatedAt()

  @@unique([collection_id, user_id, date])
  @@index([collection_id])
  @@index([user_id])
  @@index([date])
}
