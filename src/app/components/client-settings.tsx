'use client';

import {
	Button,
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuLabel,
	DropdownMenuTrigger,
	useTheme,
} from '@/components/ui';
import { useTranslation } from '@/contexts';
import { Language } from '@prisma/client';
import { Languages, Monitor, Moon, Settings, Sun } from 'lucide-react';

export function ClientSettings() {
	const { theme, setTheme } = useTheme();
	const { language, setLanguage, t } = useTranslation();

	return (
		<>
			{/* Combined Float Menu */}
			<DropdownMenu>
				<DropdownMenuTrigger asChild>
					<Button
						size="icon"
						className="fixed bottom-6 right-6 z-50 h-14 w-14 rounded-full shadow-lg"
					>
						<Settings className="h-6 w-6" />
					</Button>
				</DropdownMenuTrigger>
				<DropdownMenuContent align="end" className="mb-1 mr-1 w-56">
					<DropdownMenuLabel>Language</DropdownMenuLabel>
					<DropdownMenuItem
						onClick={() => setLanguage(Language.EN)}
						className={`cursor-pointer ${language === Language.EN ? 'bg-accent' : ''}`}
					>
						<Languages className="h-4 w-4 mr-2" />
						English
					</DropdownMenuItem>
					<DropdownMenuItem
						onClick={() => setLanguage(Language.VI)}
						className={`cursor-pointer ${language === Language.VI ? 'bg-accent' : ''}`}
					>
						<Languages className="h-4 w-4 mr-2" />
						Tiếng Việt
					</DropdownMenuItem>

					<DropdownMenuLabel>Theme</DropdownMenuLabel>
					<DropdownMenuItem
						onClick={() => setTheme('light')}
						className={`cursor-pointer ${theme === 'light' ? 'bg-accent' : ''}`}
					>
						<Sun className="h-4 w-4 mr-2" />
						{t('theme.light')}
					</DropdownMenuItem>
					<DropdownMenuItem
						onClick={() => setTheme('dark')}
						className={`cursor-pointer ${theme === 'dark' ? 'bg-accent' : ''}`}
					>
						<Moon className="h-4 w-4 mr-2" />
						{t('theme.dark')}
					</DropdownMenuItem>
					<DropdownMenuItem
						onClick={() => setTheme('system')}
						className={`cursor-pointer ${theme === 'system' ? 'bg-accent' : ''}`}
					>
						<Monitor className="h-4 w-4 mr-2" />
						{t('theme.system')}
					</DropdownMenuItem>
				</DropdownMenuContent>
			</DropdownMenu>
		</>
	);
}
