'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { LoginForm } from '@/components/auth/login-form';
import { useTranslation } from '@/contexts/translation-context';
import { useAuth } from '@/contexts/auth-context';
import { LoadingSpinner } from '@/components/ui';

export function LoginClient() {
	const { t } = useTranslation();
	const { user, isLoading, getUser } = useAuth();
	const router = useRouter();

	useEffect(() => {
		const initAuth = async () => {
			if (!user && !isLoading) {
				await getUser();
			}
		};

		initAuth();
	}, [user, isLoading, getUser]);

	useEffect(() => {
		if (user) {
			router.push('/');
		}
	}, [user, router]);

	if (isLoading) {
		return (
			<div className="min-h-screen flex items-center justify-center">
				<LoadingSpinner size="lg" />
			</div>
		);
	}

	if (user) {
		return null; // Will redirect in useEffect
	}

	return (
		<div className="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
			<div className="max-w-md w-full space-y-8">
				<div className="text-center">
					<h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-2">
						{t('auth.login.title')}
					</h1>
					<p className="text-gray-600 dark:text-gray-400">{t('auth.login.welcome')}</p>
				</div>
				<LoginForm />
			</div>
		</div>
	);
}
