'use server';

import { verifyToken } from '@/backend/utils';
import { getAuthService } from '@/backend/wire'; // Import the auth service getter
import { getAuthConfig, getServerConfig } from '@/config';
import { Provider } from '@prisma/client';
import type { NextRequest } from 'next/server';

export async function authMiddleware(request: NextRequest) {
	const serverConfig = await getServerConfig();
	const authConfig = await getAuthConfig();
	
	// Check if we're in development mode
	if (serverConfig.env === 'development') {
		// In development, get or create the default user from provider id and provider name using AuthService
		const defaultProviderId = authConfig.defaultUser.provider_id;
		const defaultProvider = authConfig.defaultUser.provider as Provider;

		try {
			const authService = getAuthService(); // Get auth service instance
			// Use the service method that finds or creates the user
			const user = await authService.findOrCreateUserByProvider(
				defaultProvider,
				defaultProviderId
			);

			if (user?.id) {
				request.headers.set('x-user-id', user.id);
				return request;
			}
		} catch (error) {
			console.error('Error in development mode user handling:', error);
			// Fall through to production logic or return unauthorized if service fails
		}
	}

	// In production, extract token from cookie
	const token = request.cookies.get(authConfig.jwtCookieName)?.value;

	if (!token) {
		return Response.json({ error: 'Unauthorized - No token provided' }, { status: 401 });
	}

	// Get JWT secret from config
	const jwtSecret = authConfig.jwtSecret;
	if (!jwtSecret) {
		console.error('JWT secret is not defined in environment variables');
		return Response.json({ error: 'Internal server error' }, { status: 500 });
	}

	try {
		// Verify and decode token using the verify function
		const payload = await verifyToken(token);
		const userId = payload.sub;

		if (!userId) {
			return Response.json(
				{ error: 'Unauthorized - Invalid user ID in token' },
				{ status: 401 }
			);
		}

		request.headers.set('x-user-id', userId);

		return request;
	} catch (error) {
		console.error('Token verification failed:', error);
		return Response.json({ error: 'Unauthorized - Invalid token' }, { status: 401 });
	}
}
