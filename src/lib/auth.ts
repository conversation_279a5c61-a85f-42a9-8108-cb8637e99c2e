'use server';

import { cookies } from 'next/headers';
import { getAuthConfig, getServerConfig } from '@/config';
import { verifyToken } from '@/backend/utils/token.util';
import { getAuthService } from '@/backend/wire';
import { Provider } from '@prisma/client';

/**
 * Authentication function for server components and API routes
 * In development, it uses the default user from config
 * In production, it verifies the JWT token from cookies
 */
export async function auth(): Promise<{
	user: { id: string };
} | null> {
	const serverConfig = await getServerConfig();
	const authConfig = await getAuthConfig();

	// Check if we're in development mode
	if (serverConfig.env === 'development') {
		// In development, get or create the default user from provider id and provider name using AuthService
		const defaultProviderId = authConfig.defaultUser.provider_id;
		const defaultProvider = authConfig.defaultUser.provider as Provider;

		try {
			const authService = getAuthService();
			// Use the service method that finds or creates the user
			const user = await authService.findOrCreateUserByProvider(
				defaultProvider,
				defaultProviderId
			);

			if (user?.id) {
				return { user: { id: user.id } };
			}
		} catch (error) {
			console.error('Error in development mode user handling:', error);
			// In development, if service fails, return a default user
			return { user: { id: 'dev-user-id' } };
		}
	}

	// In production, verify the token
	const cookieStore = await cookies();
	const token = cookieStore.get(authConfig.jwtCookieName)?.value;
	if (!token) return null;

	try {
		const payload = await verifyToken(token);

		const userId = payload.sub;
		if (!userId) return null;

		return { user: { id: userId } };
	} catch {
		return null;
	}
}
