'use server';

import { cookies } from 'next/headers';
import { getAuthConfig } from '@/config';
import { verifyToken } from '@/backend/utils/token.util';

/**
 * Authentication function for server components and API routes
 * In development, it uses the default user from config
 * In production, it verifies the JWT token from cookies
 */
export async function auth(): Promise<{
	user: { id: string };
} | null> {
	const cookieStore = await cookies();
	const authConfig = await getAuthConfig();
	// In production, verify the token
	const token = cookieStore.get(authConfig.jwtCookieName)?.value;
	if (!token) return null;

	try {
		const payload = await verifyToken(token);

		const userId = payload.sub;
		if (!userId) return null;

		return { user: { id: userId } };
	} catch (error) {
		return null;
	}
}
